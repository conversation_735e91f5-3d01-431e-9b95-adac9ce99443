#include "UMG/DruidsChatWidgetManager.h"
#include "UMG/DruidsChatWidgetFactory.h"
#include "Blueprint/UserWidget.h"
#include "Engine/Engine.h"
#include "Engine/Blueprint.h"
#include "UObject/ConstructorHelpers.h"
#include "LogDruids.h"

// Static member definitions
TWeakObjectPtr<UDruidsChatWidgetManager> UDruidsChatWidgetManager::CachedInstance;
const FString UDruidsChatWidgetManager::BlueprintPath = TEXT("/Druids/DruidsSage/Chat/BP_DruidsChatWidgetManager");

void UDruidsChatWidgetManager::InitializeManager()
{
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::InitializeManager - Initializing chat widget manager"));

    LoadDefaultFactory();
    LoadConfigurationFromSettings();
    InitializeWidgetPools();

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::InitializeManager - Chat widget manager initialized successfully"));
}

void UDruidsChatWidgetManager::CleanupManager()
{
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::CleanupManager - Cleaning up chat widget manager"));

    CleanupWidgetPools();

    if (CurrentFactory)
    {
        CurrentFactory = nullptr;
    }

    ActiveWidgets.Empty();

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::CleanupManager - Chat widget manager cleaned up"));
}

UDruidsChatWidgetManager* UDruidsChatWidgetManager::Get(const UObject* WorldContext)
{
    // Check if we have a valid cached instance
    if (CachedInstance.IsValid())
    {
        return CachedInstance.Get();
    }

    // Try to load from Blueprint
    UDruidsChatWidgetManager* NewInstance = LoadManagerFromBlueprint();
    if (NewInstance)
    {
        CachedInstance = NewInstance;
        // Initialize the manager
        NewInstance->InitializeManager();
        return NewInstance;
    }

    UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetManager::Get - Could not load widget manager from Blueprint"));
    return nullptr;
}

void UDruidsChatWidgetManager::SetWidgetFactory(UDruidsChatWidgetFactory* NewFactory)
{
    if (NewFactory != CurrentFactory)
    {
        CurrentFactory = NewFactory;
        
        if (CurrentFactory)
        {
            ApplyConfigurationToFactory();
        }
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::SetWidgetFactory - Widget factory set to %s"), 
            CurrentFactory ? *CurrentFactory->GetClass()->GetName() : TEXT("nullptr"));
    }
}

UDruidsChatWidgetFactory* UDruidsChatWidgetManager::GetWidgetFactory()
{
    if (!CurrentFactory)
    {
        LoadDefaultFactory();
    }
    
    return CurrentFactory;
}

void UDruidsChatWidgetManager::RefreshWidgetTemplates()
{
    if (CurrentFactory)
    {
        // Refresh master configuration first (highest priority)
        CurrentFactory->DiscoverMasterConfigurationBlueprint();

        // Then refresh individual templates
        CurrentFactory->DiscoverWidgetTemplates();

        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::RefreshWidgetTemplates - Widget templates and master configuration refreshed"));
    }
    else
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetManager::RefreshWidgetTemplates - No widget factory available"));
    }
}

void UDruidsChatWidgetManager::EnableWidgetPooling(bool bEnable)
{
    if (bWidgetPoolingEnabled != bEnable)
    {
        bWidgetPoolingEnabled = bEnable;
        
        if (!bWidgetPoolingEnabled)
        {
            // Clear pools if pooling is disabled
            CleanupWidgetPools();
        }
        else
        {
            // Initialize pools if pooling is enabled
            InitializeWidgetPools();
        }
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::EnableWidgetPooling - Widget pooling %s"), 
            bWidgetPoolingEnabled ? TEXT("enabled") : TEXT("disabled"));
    }
}

void UDruidsChatWidgetManager::SetPoolSize(int32 NewPoolSize)
{
    if (NewPoolSize > 0 && NewPoolSize != WidgetPoolSize)
    {
        WidgetPoolSize = NewPoolSize;
        
        // Adjust existing pools to new size
        if (bWidgetPoolingEnabled)
        {
            CleanupWidgetPools();
            InitializeWidgetPools();
        }
        
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::SetPoolSize - Pool size set to %d"), WidgetPoolSize);
    }
}

void UDruidsChatWidgetManager::RegisterActiveWidget(UUserWidget* Widget)
{
    if (Widget && !ActiveWidgets.Contains(Widget))
    {
        ActiveWidgets.Add(Widget);
        UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetManager::RegisterActiveWidget - Registered widget %s"), 
            *Widget->GetClass()->GetName());
    }
}

void UDruidsChatWidgetManager::UnregisterActiveWidget(UUserWidget* Widget)
{
    if (Widget)
    {
        ActiveWidgets.RemoveAll([Widget](const TWeakObjectPtr<UUserWidget>& WeakWidget)
        {
            return WeakWidget.Get() == Widget;
        });
        
        UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetManager::UnregisterActiveWidget - Unregistered widget %s"), 
            *Widget->GetClass()->GetName());
    }
}

void UDruidsChatWidgetManager::CleanupInactiveWidgets()
{
    int32 InitialCount = ActiveWidgets.Num();
    
    // Remove null or invalid weak pointers
    ActiveWidgets.RemoveAll([](const TWeakObjectPtr<UUserWidget>& WeakWidget)
    {
        return !WeakWidget.IsValid();
    });
    
    int32 RemovedCount = InitialCount - ActiveWidgets.Num();
    
    if (RemovedCount > 0)
    {
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::CleanupInactiveWidgets - Cleaned up %d inactive widgets"), 
            RemovedCount);
    }
}

int32 UDruidsChatWidgetManager::GetActiveWidgetCount() const
{
    return ActiveWidgets.Num();
}

void UDruidsChatWidgetManager::ReloadConfiguration()
{
    LoadConfigurationFromSettings();
    
    if (CurrentFactory)
    {
        CurrentFactory->LoadWidgetClasses();
        ApplyConfigurationToFactory();
    }
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::ReloadConfiguration - Configuration reloaded"));
}

void UDruidsChatWidgetManager::SaveConfiguration()
{
    // Save current configuration to settings
    // This would typically involve updating the UDruidsChatStyleSettings
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::SaveConfiguration - Configuration saved"));
}

void UDruidsChatWidgetManager::LogWidgetFactoryStatus()
{
    UE_LOG(LogDruidsSage, Log, TEXT("=== Widget Factory Status ==="));
    UE_LOG(LogDruidsSage, Log, TEXT("Factory: %s"), CurrentFactory ? *CurrentFactory->GetClass()->GetName() : TEXT("None"));
    UE_LOG(LogDruidsSage, Log, TEXT("Widget Pooling: %s"), bWidgetPoolingEnabled ? TEXT("Enabled") : TEXT("Disabled"));
    UE_LOG(LogDruidsSage, Log, TEXT("Pool Size: %d"), WidgetPoolSize);
    UE_LOG(LogDruidsSage, Log, TEXT("Active Widgets: %d"), GetActiveWidgetCount());
    UE_LOG(LogDruidsSage, Log, TEXT("Chat Item Pool: %d"), ChatItemPool.Num());
    UE_LOG(LogDruidsSage, Log, TEXT("Chat View Pool: %d"), ChatViewPool.Num());
    UE_LOG(LogDruidsSage, Log, TEXT("Chat Shell Pool: %d"), ChatShellPool.Num());
    UE_LOG(LogDruidsSage, Log, TEXT("============================="));
}

TArray<FString> UDruidsChatWidgetManager::GetActiveWidgetInfo()
{
    TArray<FString> WidgetInfo;
    
    for (const TWeakObjectPtr<UUserWidget>& WeakWidget : ActiveWidgets)
    {
        if (UUserWidget* Widget = WeakWidget.Get())
        {
            FString Info = FString::Printf(TEXT("%s - %s"), 
                *Widget->GetClass()->GetName(), 
                Widget->IsInViewport() ? TEXT("In Viewport") : TEXT("Not In Viewport"));
            WidgetInfo.Add(Info);
        }
    }
    
    return WidgetInfo;
}

void UDruidsChatWidgetManager::LoadDefaultFactory()
{
    if (!CurrentFactory)
    {
        CurrentFactory = NewObject<UDruidsChatWidgetFactory>(this);
        UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::LoadDefaultFactory - Created default widget factory"));
    }
}

void UDruidsChatWidgetManager::InitializeWidgetPools()
{
    if (!bWidgetPoolingEnabled)
    {
        return;
    }
    
    // Initialize empty pools - widgets will be created on demand
    ChatItemPool.Reserve(WidgetPoolSize);
    ChatViewPool.Reserve(WidgetPoolSize / 10); // Fewer chat views needed
    ChatShellPool.Reserve(WidgetPoolSize / 20); // Even fewer chat shells needed
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::InitializeWidgetPools - Widget pools initialized"));
}

void UDruidsChatWidgetManager::CleanupWidgetPools()
{
    CleanupPool(ChatItemPool);
    CleanupPool(ChatViewPool);
    CleanupPool(ChatShellPool);
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::CleanupWidgetPools - Widget pools cleaned up"));
}

UUserWidget* UDruidsChatWidgetManager::GetPooledWidget(TArray<TWeakObjectPtr<UUserWidget>>& Pool, UClass* WidgetClass)
{
    if (!bWidgetPoolingEnabled || !WidgetClass)
    {
        return nullptr;
    }
    
    // Find a widget of the correct class in the pool
    for (int32 i = Pool.Num() - 1; i >= 0; --i)
    {
        if (UUserWidget* Widget = Pool[i].Get())
        {
            if (Widget->GetClass() == WidgetClass)
            {
                Pool.RemoveAt(i);
                return Widget;
            }
        }
        else
        {
            // Remove invalid weak pointer
            Pool.RemoveAt(i);
        }
    }
    
    return nullptr;
}

void UDruidsChatWidgetManager::ReturnWidgetToPool(UUserWidget* Widget, TArray<TWeakObjectPtr<UUserWidget>>& Pool)
{
    if (!bWidgetPoolingEnabled || !Widget || Pool.Num() >= WidgetPoolSize)
    {
        return;
    }
    
    // Reset widget state before returning to pool
    Widget->RemoveFromParent();
    
    Pool.Add(Widget);
}

void UDruidsChatWidgetManager::CleanupPool(TArray<TWeakObjectPtr<UUserWidget>>& Pool)
{
    // Remove invalid weak pointers and destroy widgets
    for (int32 i = Pool.Num() - 1; i >= 0; --i)
    {
        if (UUserWidget* Widget = Pool[i].Get())
        {
            Widget->RemoveFromParent();
            // Widget will be garbage collected
        }
        Pool.RemoveAt(i);
    }
}

void UDruidsChatWidgetManager::LoadConfigurationFromSettings()
{
    // Load configuration from UDruidsChatStyleSettings when available
    bWidgetPoolingEnabled = true; // Default value
    WidgetPoolSize = 50; // Default value
    
    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::LoadConfigurationFromSettings - Configuration loaded from settings"));
}

void UDruidsChatWidgetManager::ApplyConfigurationToFactory()
{
    if (CurrentFactory)
    {
        // Apply any configuration that needs to be passed to the factory
        UE_LOG(LogDruidsSage, Verbose, TEXT("UDruidsChatWidgetManager::ApplyConfigurationToFactory - Configuration applied to factory"));
    }
}

UDruidsChatWidgetManager* UDruidsChatWidgetManager::LoadManagerFromBlueprint()
{
    // Load the Blueprint class
    UBlueprint* ManagerBlueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
    if (!ManagerBlueprint)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetManager::LoadManagerFromBlueprint - Could not load Blueprint from path: %s"), *BlueprintPath);
        return nullptr;
    }

    // Get the generated class from the Blueprint
    UClass* ManagerClass = ManagerBlueprint->GeneratedClass;
    if (!ManagerClass)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetManager::LoadManagerFromBlueprint - Blueprint has no generated class: %s"), *BlueprintPath);
        return nullptr;
    }

    // Verify it's the correct type
    if (!ManagerClass->IsChildOf(UDruidsChatWidgetManager::StaticClass()))
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("UDruidsChatWidgetManager::LoadManagerFromBlueprint - Blueprint class is not a child of UDruidsChatWidgetManager: %s"), *BlueprintPath);
        return nullptr;
    }

    // Create an instance of the Blueprint class
    UDruidsChatWidgetManager* NewInstance = NewObject<UDruidsChatWidgetManager>(GetTransientPackage(), ManagerClass);
    if (!NewInstance)
    {
        UE_LOG(LogDruidsSage, Error, TEXT("UDruidsChatWidgetManager::LoadManagerFromBlueprint - Failed to create instance of Blueprint class: %s"), *BlueprintPath);
        return nullptr;
    }

    UE_LOG(LogDruidsSage, Log, TEXT("UDruidsChatWidgetManager::LoadManagerFromBlueprint - Successfully loaded manager from Blueprint: %s"), *BlueprintPath);
    return NewInstance;
}
