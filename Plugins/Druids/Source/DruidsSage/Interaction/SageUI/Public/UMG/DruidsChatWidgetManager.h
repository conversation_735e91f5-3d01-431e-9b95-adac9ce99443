#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "DruidsChatWidgetManager.generated.h"

// Forward declarations
class UDruidsChatWidgetFactory;

/**
 * Subsystem for managing chat widget creation and lifecycle
 * Provides centralized access to the widget factory and manages widget pooling
 */
UCLASS(BlueprintType)
class SAGEUI_API UDruidsChatWidgetManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // Subsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // Static access method
    UFUNCTION(BlueprintCallable, Category = "Druids Chat", meta = (CallInEditor = "true"))
    static UDruidsChatWidgetManager* Get(const UObject* WorldContext);

    // Widget factory management
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    void SetWidgetFactory(UDruidsChatWidgetFactory* NewFactory);

    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    UDruidsChatWidgetFactory* GetWidgetFactory();

    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    void RefreshWidgetTemplates();

    // Widget pooling (for performance optimization)
    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    void EnableWidgetPooling(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    bool IsWidgetPoolingEnabled() const { return bWidgetPoolingEnabled; }

    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    void SetPoolSize(int32 NewPoolSize);

    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    int32 GetPoolSize() const { return WidgetPoolSize; }

    // Widget lifecycle management
    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    void RegisterActiveWidget(UUserWidget* Widget);

    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    void UnregisterActiveWidget(UUserWidget* Widget);

    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    void CleanupInactiveWidgets();

    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    int32 GetActiveWidgetCount() const;

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void ReloadConfiguration();

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void SaveConfiguration();

    // Debug and diagnostics
    UFUNCTION(BlueprintCallable, Category = "Debug", meta = (CallInEditor = "true"))
    void LogWidgetFactoryStatus();

    UFUNCTION(BlueprintCallable, Category = "Debug", meta = (CallInEditor = "true"))
    TArray<FString> GetActiveWidgetInfo();

protected:
    // Widget factory instance
    UPROPERTY()
    UDruidsChatWidgetFactory* CurrentFactory;

    // Widget pooling settings
    UPROPERTY()
    bool bWidgetPoolingEnabled = true;

    UPROPERTY()
    int32 WidgetPoolSize = 50;

    // Active widget tracking
    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ActiveWidgets;

    // Widget pools for different types
    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatItemPool;

    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatViewPool;

    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatShellPool;

private:
    // Internal initialization
    void LoadDefaultFactory();
    void InitializeWidgetPools();
    void CleanupWidgetPools();

    // Pool management helpers
    UUserWidget* GetPooledWidget(TArray<TWeakObjectPtr<UUserWidget>>& Pool, UClass* WidgetClass);
    void ReturnWidgetToPool(UUserWidget* Widget, TArray<TWeakObjectPtr<UUserWidget>>& Pool);
    void CleanupPool(TArray<TWeakObjectPtr<UUserWidget>>& Pool);

    // Configuration helpers
    void LoadConfigurationFromSettings();
    void ApplyConfigurationToFactory();
};
