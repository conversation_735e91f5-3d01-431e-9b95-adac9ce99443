#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/Blueprint.h"
#include "DruidsChatWidgetManager.generated.h"

// Forward declarations
class UDruidsChatWidgetFactory;

/**
 * Blueprint-based manager for chat widget creation and lifecycle
 * Provides centralized access to the widget factory and manages widget pooling
 * This class is designed to be extended in Blueprint and instantiated from the plugin content folder
 */
UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsChatWidgetManager : public UObject
{
    GENERATED_BODY()

public:
    // Manual initialization method (replaces subsystem Initialize)
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    virtual void InitializeManager();

    // Manual cleanup method (replaces subsystem Deinitialize)
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    virtual void CleanupManager();

    // Static access method - loads Blueprint from plugin content folder
    UFUNCTION(BlueprintCallable, Category = "Druids Chat", meta = (CallInEditor = "true"))
    static UDruidsChatWidgetManager* Get(const UObject* WorldContext = nullptr);

    // Widget factory management
    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    void SetWidgetFactory(UDruidsChatWidgetFactory* NewFactory);

    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    UDruidsChatWidgetFactory* GetWidgetFactory();

    UFUNCTION(BlueprintCallable, Category = "Druids Chat")
    void RefreshWidgetTemplates();

    // Widget pooling (for performance optimization)
    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    void EnableWidgetPooling(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    bool IsWidgetPoolingEnabled() const { return bWidgetPoolingEnabled; }

    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    void SetPoolSize(int32 NewPoolSize);

    UFUNCTION(BlueprintCallable, Category = "Widget Pooling")
    int32 GetPoolSize() const { return WidgetPoolSize; }

    // Widget lifecycle management
    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    void RegisterActiveWidget(UUserWidget* Widget);

    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    void UnregisterActiveWidget(UUserWidget* Widget);

    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    void CleanupInactiveWidgets();

    UFUNCTION(BlueprintCallable, Category = "Widget Management")
    int32 GetActiveWidgetCount() const;

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void ReloadConfiguration();

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void SaveConfiguration();

    // Debug and diagnostics
    UFUNCTION(BlueprintCallable, Category = "Debug", meta = (CallInEditor = "true"))
    void LogWidgetFactoryStatus();

    UFUNCTION(BlueprintCallable, Category = "Debug", meta = (CallInEditor = "true"))
    TArray<FString> GetActiveWidgetInfo();

protected:
    // Widget factory instance
    UPROPERTY()
    UDruidsChatWidgetFactory* CurrentFactory;

    // Widget pooling settings
    UPROPERTY()
    bool bWidgetPoolingEnabled = true;

    UPROPERTY()
    int32 WidgetPoolSize = 50;

    // Active widget tracking
    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ActiveWidgets;

    // Widget pools for different types
    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatItemPool;

    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatViewPool;

    UPROPERTY()
    TArray<TWeakObjectPtr<UUserWidget>> ChatShellPool;

private:
    // Internal initialization
    void LoadDefaultFactory();
    void InitializeWidgetPools();
    void CleanupWidgetPools();

    // Pool management helpers
    UUserWidget* GetPooledWidget(TArray<TWeakObjectPtr<UUserWidget>>& Pool, UClass* WidgetClass);
    void ReturnWidgetToPool(UUserWidget* Widget, TArray<TWeakObjectPtr<UUserWidget>>& Pool);
    void CleanupPool(TArray<TWeakObjectPtr<UUserWidget>>& Pool);

    // Configuration helpers
    void LoadConfigurationFromSettings();
    void ApplyConfigurationToFactory();

    // Static instance management
    static TWeakObjectPtr<UDruidsChatWidgetManager> CachedInstance;
    static UDruidsChatWidgetManager* LoadManagerFromBlueprint();

    // Blueprint path in plugin content folder
    static const FString BlueprintPath;
};
